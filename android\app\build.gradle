plugins {
    id "com.android.application"
    id "kotlin-android"
    id "kotlin-parcelize"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.example.daegu_bus_app"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
        encoding = "UTF-8"
        coreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.daegu_bus_app"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        // multidex 활성화 (필요한 경우)
        multiDexEnabled true
        buildConfigField "String", "KAKAO_NATIVE_APP_KEY", "\"\""
        externalNativeBuild {
            cmake {
                cppFlags ""
                arguments "-DANDROID_STL=c++_shared"
            }
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.debug
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // R8 동작 방식 완화 설정 추가
            crunchPngs false // PNG 최적화 비활성화
            
            // proguardOptions 블록 제거
            // 대신 다음과 같이 설정
            ndk {
                abiFilters 'armeabi-v7a', 'arm64-v8a'
            }
            
            buildConfigField "boolean", "ENABLE_LOGGING", "true"
            buildConfigField "boolean", "ENABLE_LOGS", "true"
        }
        
        debug {
            buildConfigField "boolean", "ENABLE_LOGGING", "true"
        }
    }

    buildFeatures {
        buildConfig true
    }
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
    // 기존 의존성 유지
    
    // Retrofit과 OkHttp
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.10.0'
    
    // JSON 처리
    implementation 'com.google.code.gson:gson:2.9.1'
    
    // HTML 파싱
    implementation 'org.jsoup:jsoup:1.15.3'
    
    // 코루틴
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'
    
    // R8 오류 해결을 위한 androidx.window 관련 의존성
    implementation 'androidx.window:window:1.0.0'

    // 포그라운드 서비스 및 알림을 위한 androidx 종속성
    implementation 'androidx.core:core-ktx:1.9.0'

    // WorkManager
    implementation "androidx.work:work-runtime-ktx:2.8.1"

    // 추가된 종속성
    implementation 'androidx.window:window-java:1.0.0'
    implementation 'com.google.android.play:core:1.10.3'

    // Multidex 지원 추가 (필요한 경우)
    implementation 'androidx.multidex:multidex:2.0.1'

    // Material Design
    implementation 'com.google.android.material:material:1.11.0'
}

flutter {
    source = "../.."
}
