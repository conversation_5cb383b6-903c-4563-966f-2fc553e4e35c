import 'dart:async';
import 'dart:convert';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:http/http.dart' as http;
import 'package:xml/xml.dart' as xml;
import '../models/auto_alarm.dart';
import '../models/alarm_data.dart' as alarm_model;
import '../utils/simple_tts_helper.dart';
import 'notification_service.dart';
import 'settings_service.dart';
import '../main.dart' show logMessage, LogLevel;
import '../utils/database_helper.dart';

class CachedBusInfo {
  int remainingMinutes;
  String currentStation;
  String stationName;
  String busNo;
  String routeId;
  DateTime _lastUpdated;

  CachedBusInfo({
    required this.remainingMinutes,
    required this.currentStation,
    required this.stationName,
    required this.busNo,
    required this.routeId,
    required DateTime lastUpdated,
  }) : _lastUpdated = lastUpdated;

  DateTime get lastUpdated => _lastUpdated;

  factory CachedBusInfo.fromBusInfo({
    required dynamic busInfo,
    required String busNumber,
    required String routeId,
  }) {
    return CachedBusInfo(
      remainingMinutes: busInfo.getRemainingMinutes(),
      currentStation: busInfo.currentStation,
      stationName: busInfo.currentStation, // 현재 정류장을 stationName으로 사용
      busNo: busNumber,
      routeId: routeId,
      lastUpdated: DateTime.now(),
    );
  }

  int getRemainingMinutes() {
    final now = DateTime.now();
    final difference = now.difference(_lastUpdated);
    return (remainingMinutes - difference.inMinutes).clamp(0, remainingMinutes);
  }
}

class AlarmService extends ChangeNotifier {
  final NotificationService _notificationService;
  final SettingsService _settingsService;

  final Map<String, alarm_model.AlarmData> _activeAlarms = {};
  bool get _useTTS => _settingsService.useTts;
  Timer? _alarmCheckTimer;
  final List<alarm_model.AlarmData> _autoAlarms = [];
  bool _initialized = false;
  final Map<String, CachedBusInfo> _cachedBusInfo = {};
  MethodChannel? _methodChannel;
  bool _isInTrackingMode = false;
  String? _trackedRouteId;
  final Set<String> _processedNotifications = {};
  Timer? _refreshTimer;

  // 자동 알람 제어 플래그
  bool _autoAlarmEnabled = true;
  final Set<String> _manuallyStoppedAlarms = <String>{};
  final Map<String, DateTime> _manuallyStoppedTimestamps = <String, DateTime>{};

  // 실행된 알람 추적 (중복 실행 방지)
  final Map<String, DateTime> _executedAlarms = <String, DateTime>{};

  // 중복 이벤트 방지를 위한 캐시
  final Map<String, int> _processedEventTimestamps = <String, int>{};

  // 사용자 수동 중지 후 재시작 방지를 위한 플래그
  bool _userManuallyStopped = false;
  int _lastManualStopTime = 0;
  static const int _restartPreventionDuration = 30000; // 30초간 재시작 방지

  List<alarm_model.AlarmData> get activeAlarms {
    final allAlarms = <alarm_model.AlarmData>{};
    allAlarms.addAll(
      _activeAlarms.values.where((alarm) => !alarm.isAutoAlarm),
    ); // 일반 알람만 추가
    allAlarms.addAll(
      _autoAlarms.where((alarm) => alarm.isAutoAlarm),
    ); // 활성화된 자동 알람만 추가
    return allAlarms.toList();
  }

  List<alarm_model.AlarmData> get autoAlarms => _autoAlarms;
  bool get isInTrackingMode => _isInTrackingMode;

  AlarmService({
    required NotificationService notificationService,
    required SettingsService settingsService,
  })  : _notificationService = notificationService,
        _settingsService = settingsService {
    _setupMethodChannel();
  }

  void _setupMethodChannel() {
    _methodChannel = const MethodChannel('com.example.daegu_bus_app/bus_api');
    _methodChannel?.setMethodCallHandler(_handleMethodCall);
  }

  Future<dynamic> _handleMethodCall(MethodCall call) async {
    try {
      switch (call.method) {
        case 'onAlarmCanceledFromNotification':
          final Map<String, dynamic> args = Map<String, dynamic>.from(
            call.arguments,
          );
          final String busNo = args['busNo'] ?? '';
          final String routeId = args['routeId'] ?? '';
          final String stationName = args['stationName'] ?? '';
          final int? timestamp = args['timestamp'];

          // 중복 이벤트 방지 체크
          final String eventKey =
              "${busNo}_${routeId}_${stationName}_cancellation";
          if (timestamp != null &&
              _processedEventTimestamps.containsKey(eventKey)) {
            final lastTimestamp = _processedEventTimestamps[eventKey]!;
            final timeDiff = timestamp - lastTimestamp;
            if (timeDiff < 3000) {
              // 3초 이내 중복 이벤트 무시
              logMessage(
                '⚠️ [중복방지] 이벤트 무시: $eventKey (${timeDiff}ms 전에 처리됨)',
                level: LogLevel.warning,
              );
              return true;
            }
          }

          // 이벤트 시간 기록
          if (timestamp != null) {
            _processedEventTimestamps[eventKey] = timestamp;
          }

          // 오래된 이벤트 정리 (30초 이전)
          final now = DateTime.now().millisecondsSinceEpoch;
          final expiredKeys = _processedEventTimestamps.entries
              .where((entry) => now - entry.value > 30000)
              .map((entry) => entry.key)
              .toList();
          for (var key in expiredKeys) {
            _processedEventTimestamps.remove(key);
          }

          logMessage(
            '🔔 [노티피케이션] 네이티브에서 특정 알람 취소 이벤트 수신: $busNo번, $stationName, $routeId',
            level: LogLevel.info,
          );

          // 즉시 Flutter 측 상태 동기화 (낙관적 업데이트)
          final String alarmKey = "${busNo}_${stationName}_$routeId";
          final removedAlarm = _activeAlarms.remove(alarmKey);

          if (removedAlarm != null) {
            // 수동으로 중지된 알람으로 표시 (자동 알람 재시작 방지)
            _manuallyStoppedAlarms.add(alarmKey);
            _manuallyStoppedTimestamps[alarmKey] = DateTime.now();
            logMessage('🚫 수동 중지 알람 추가: $alarmKey', level: LogLevel.info);

            // 캐시 정리
            final cacheKey = "${busNo}_$routeId";
            _cachedBusInfo.remove(cacheKey);

            // 추적 상태 업데이트
            if (_trackedRouteId == routeId) {
              _trackedRouteId = null;
              if (_activeAlarms.isEmpty) {
                _isInTrackingMode = false;
                logMessage(
                  '🛑 추적 모드 비활성화 (취소된 알람이 추적 중이던 알람)',
                  level: LogLevel.info,
                );
              }
            } else if (_activeAlarms.isEmpty) {
              _isInTrackingMode = false;
              _trackedRouteId = null;
              logMessage('🛑 추적 모드 비활성화 (모든 알람 취소됨)', level: LogLevel.info);
            }

            // 상태 저장 및 UI 업데이트
            await _saveAlarms();
            notifyListeners();

            logMessage(
              '✅ 네이티브 이벤트에 따른 Flutter 알람 동기화 완료: $alarmKey',
              level: LogLevel.info,
            );
          } else {
            logMessage(
              '⚠️ 해당 알람($alarmKey)이 Flutter에 없음 - 상태 정리만 수행',
              level: LogLevel.warning,
            );

            // 상태 정리
            if (_activeAlarms.isEmpty && _isInTrackingMode) {
              _isInTrackingMode = false;
              _trackedRouteId = null;
              notifyListeners();
              logMessage('🛑 추적 모드 비활성화 (상태 정리)', level: LogLevel.info);
            }
          }

          return true; // Acknowledge event received
        case 'onAllAlarmsCanceled':
          // 모든 알람 취소 이벤트 처리
          logMessage(
            '🛑🛑🛑 네이티브에서 모든 알람 취소 이벤트 수신 - 사용자가 "추적 중지" 버튼을 눌렀습니다!',
            level: LogLevel.warning,
          );

          // 🛑 사용자 수동 중지 플래그 설정 (30초간 자동 알람 재시작 방지)
          _userManuallyStopped = true;
          _lastManualStopTime = DateTime.now().millisecondsSinceEpoch;
          logMessage(
            '🛑 Flutter 측 수동 중지 플래그 설정 - 30초간 자동 알람 재시작 방지',
            level: LogLevel.warning,
          );

          // 🛑 실시간 버스 업데이트 타이머 중지 (중요!)
          try {
            _notificationService.stopRealTimeBusUpdates();
            logMessage('🛑 실시간 버스 업데이트 타이머 강제 중지 완료', level: LogLevel.info);
          } catch (e) {
            logMessage('❌ 실시간 버스 업데이트 타이머 중지 오류: $e', level: LogLevel.error);
          }

          // 모든 활성 알람 제거
          if (_activeAlarms.isNotEmpty) {
            // 모든 활성 알람을 수동 중지 목록에 추가
            final now = DateTime.now();
            for (var alarmKey in _activeAlarms.keys) {
              _manuallyStoppedAlarms.add(alarmKey);
              _manuallyStoppedTimestamps[alarmKey] = now;
            }
            logMessage(
              '🚫 모든 알람을 수동 중지 목록에 추가: ${_activeAlarms.length}개',
              level: LogLevel.info,
            );

            _activeAlarms.clear();
            _cachedBusInfo.clear();
            _isInTrackingMode = false;
            _trackedRouteId = null;
            await _saveAlarms();
            logMessage('✅ 모든 알람 취소 완료 (네이티브 이벤트에 의해)', level: LogLevel.info);
            notifyListeners();
          }

          return true;
        case 'stopAutoAlarmFromBroadcast':
          // 자동알람 중지 브로드캐스트 수신 처리
          final Map<String, dynamic> args = Map<String, dynamic>.from(
            call.arguments,
          );
          final String busNo = args['busNo'] ?? '';
          final String stationName = args['stationName'] ?? '';
          final String routeId = args['routeId'] ?? '';

          logMessage(
            '🔔 네이티브에서 자동알람 중지 브로드캐스트 수신: $busNo, $stationName, $routeId',
            level: LogLevel.info,
          );

          // stopAutoAlarm 메서드 호출
          try {
            final result = await stopAutoAlarm(busNo, stationName, routeId);
            if (result) {
              logMessage(
                '✅ 자동알람 중지 완료 (브로드캐스트에 의해): $busNo번',
                level: LogLevel.info,
              );
            } else {
              logMessage(
                '❌ 자동알람 중지 실패 (브로드캐스트에 의해): $busNo번',
                level: LogLevel.error,
              );
            }
          } catch (e) {
            logMessage('❌ 자동알람 중지 처리 오류: $e', level: LogLevel.error);
          }

          return true;
        default:
          // Ensure other method calls are still handled if any exist
          logMessage(
            'Unhandled method call: ${call.method}',
            level: LogLevel.warning,
          );
          return null;
      }
    } catch (e) {
      logMessage('메서드 채널 핸들러 오류 (${call.method}): $e', level: LogLevel.error);
      return null;
    }
  }

  Future<void> initialize() async {
    if (_initialized) return;
    _initialized = true; // 초기화 시작을 먼저 표시

    try {
      await _notificationService.initialize();

      // 데이터 로딩을 비동기적으로 처리하여 앱 시작을 막지 않음
      _loadDataInBackground();

      _alarmCheckTimer?.cancel();
      _alarmCheckTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
        refreshAlarms();
        _checkAutoAlarms(); // 자동 알람 체크 추가 (5초마다 정밀 체크)

        // 디버깅: 현재 자동 알람 상태 출력 (30초마다)
        if (timer.tick % 6 == 0) {
          _logAutoAlarmStatus();
        }
      });

      logMessage('✅ AlarmService 초기화 시작 (데이터는 백그라운드 로딩)');
    } catch (e) {
      logMessage('❌ AlarmService 초기화 오류: $e', level: LogLevel.error);
    }
  }

  Future<void> _loadDataInBackground() async {
    await loadAlarms();
    await loadAutoAlarms();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _initialized = false;
    _alarmCheckTimer?.cancel();
    super.dispose();
  }

  // 자동 알람 일시 정지/재개 메서드
  void pauseAutoAlarms() {
    _autoAlarmEnabled = false;
    logMessage('⏸️ 자동 알람 일시 정지', level: LogLevel.info);
  }

  void resumeAutoAlarms() {
    _autoAlarmEnabled = true;
    logMessage('▶️ 자동 알람 재개', level: LogLevel.info);
  }

  void clearManuallyStoppedAlarms() {
    _manuallyStoppedAlarms.clear();
    _manuallyStoppedTimestamps.clear();
    logMessage('🧹 수동 중지 알람 목록 초기화', level: LogLevel.info);
  }

  void cleanupExecutedAlarms() {
    final now = DateTime.now();
    final cutoffTime = now.subtract(const Duration(hours: 2)); // 2시간 이전 기록 삭제

    final keysToRemove = <String>[];
    _executedAlarms.forEach((key, executionTime) {
      if (executionTime.isBefore(cutoffTime)) {
        keysToRemove.add(key);
      }
    });

    for (var key in keysToRemove) {
      _executedAlarms.remove(key);
    }

    if (keysToRemove.isNotEmpty) {
      logMessage(
        '🧹 실행 기록 정리: ${keysToRemove.length}개 제거',
        level: LogLevel.debug,
      );
    }
  }

  // 디버깅용: 자동 알람 상태 로그 출력
  void _logAutoAlarmStatus() {
    try {
      // 실행 기록 정리 (주기적으로)
      cleanupExecutedAlarms();

      final now = DateTime.now();
      final weekdays = ['일', '월', '화', '수', '목', '금', '토'];
      logMessage(
        '🕒 [자동알람 상태] 현재 시간: ${now.toString()} (${weekdays[now.weekday % 7]})',
      );
      logMessage('🕒 [자동알람 상태] 활성 자동 알람: ${_autoAlarms.length}개');
      logMessage('🕒 [자동알람 상태] 자동 알람 활성화: $_autoAlarmEnabled');
      logMessage('🕒 [자동알람 상태] 수동 중지된 알람: ${_manuallyStoppedAlarms.length}개');
      logMessage('🕒 [자동알람 상태] 실행 기록: ${_executedAlarms.length}개');

      for (var alarm in _autoAlarms) {
        final timeUntilAlarm = alarm.scheduledTime.difference(now);
        final repeatDaysStr =
            alarm.repeatDays?.map((day) => weekdays[day % 7]).join(', ') ??
                '없음';
        logMessage(
          '  - ${alarm.busNo}번 (${alarm.stationName}): 예정 시간 ${alarm.scheduledTime.toString()}, ${timeUntilAlarm.inMinutes}분 후, 반복: $repeatDaysStr',
        );
      }

      if (_autoAlarms.isEmpty) {
        logMessage('  - 설정된 자동 알람이 없습니다.');
      }

      if (_manuallyStoppedAlarms.isNotEmpty) {
        logMessage('  - 수동 중지된 알람:');
        for (var alarmKey in _manuallyStoppedAlarms) {
          final stoppedTime = _manuallyStoppedTimestamps[alarmKey];
          if (stoppedTime != null) {
            final stoppedDate = DateTime(
              stoppedTime.year,
              stoppedTime.month,
              stoppedTime.day,
            );
            final currentDate = DateTime(now.year, now.month, now.day);
            final isToday = stoppedDate.isAtSameMomentAs(currentDate);
            logMessage(
              '    • $alarmKey (중지일: ${stoppedTime.month}/${stoppedTime.day}, ${isToday ? "오늘" : "과거"})',
            );
          }
        }
      }
    } catch (e) {
      logMessage('❌ 자동 알람 상태 로그 오류: $e', level: LogLevel.error);
    }
  }

  Future<void> loadAlarms() async {
    try {
      // 백그라운드 메신저 상태 확인 및 초기화
      if (!kIsWeb) {
        try {
          final rootIsolateToken = RootIsolateToken.instance;
          if (rootIsolateToken != null) {
            BackgroundIsolateBinaryMessenger.ensureInitialized(
              rootIsolateToken,
            );
            logMessage('✅ BackgroundIsolateBinaryMessenger 초기화 성공');
          } else {
            logMessage(
              '⚠️ RootIsolateToken이 null입니다. 메인 스레드에서 실행 중인지 확인하세요.',
              level: LogLevel.warning,
            );
          }
        } catch (e) {
          logMessage(
            '⚠️ BackgroundIsolateBinaryMessenger 초기화 오류 (무시): $e',
            level: LogLevel.warning,
          );
        }
      }

      final prefs = await SharedPreferences.getInstance();
      final alarms = prefs.getStringList('alarms') ?? [];
      _activeAlarms.clear();

      for (var json in alarms) {
        try {
          final data = jsonDecode(json);
          final alarm = alarm_model.AlarmData.fromJson(data);
          if (_isAlarmValid(alarm)) {
            final key = "${alarm.busNo}_${alarm.stationName}_${alarm.routeId}";
            _activeAlarms[key] = alarm;
          }
        } catch (e) {
          logMessage('알람 데이터 파싱 오류: $e', level: LogLevel.error);
        }
      }

      logMessage('✅ 알람 로드 완료: ${_activeAlarms.length}개');
      notifyListeners();
    } catch (e) {
      logMessage('알람 로드 중 오류 발생: $e', level: LogLevel.error);
    }
  }

  bool _isAlarmValid(alarm_model.AlarmData alarm) {
    final now = DateTime.now();
    final difference = alarm.scheduledTime.difference(now);
    return difference.inMinutes > -5; // 5분 이상 지난 알람은 제외
  }

  Future<void> loadAutoAlarms() async {
    try {
      // 백그라운드 메신저 상태 확인 및 초기화
      if (!kIsWeb) {
        try {
          final rootIsolateToken = RootIsolateToken.instance;
          if (rootIsolateToken != null) {
            BackgroundIsolateBinaryMessenger.ensureInitialized(
              rootIsolateToken,
            );
            logMessage('✅ 자동 알람용 BackgroundIsolateBinaryMessenger 초기화 성공');
          } else {
            logMessage(
              '⚠️ 자동 알람 - RootIsolateToken이 null입니다',
              level: LogLevel.warning,
            );
          }
        } catch (e) {
          logMessage(
            '⚠️ 자동 알람 BackgroundIsolateBinaryMessenger 초기화 오류 (무시): $e',
            level: LogLevel.warning,
          );
        }
      }

      final prefs = await SharedPreferences.getInstance();
      final alarms = prefs.getStringList('auto_alarms') ?? [];
      logMessage('자동 알람 데이터 로드 시작: ${alarms.length}개');

      _autoAlarms.clear();

      for (var alarmJson in alarms) {
        try {
          final Map<String, dynamic> data = jsonDecode(alarmJson);

          // scheduledTime이 문자열이면 DateTime으로 변환
          if (data['scheduledTime'] is String) {
            data['scheduledTime'] = DateTime.parse(data['scheduledTime']);
          }

          // stationId가 없는 경우, stationName과 routeId로 찾아옴
          if (data['stationId'] == null || data['stationId'].isEmpty) {
            data['stationId'] = _getStationIdFromName(
              data['stationName'],
              data['routeId'],
            );
          }

          // 필수 필드 검증
          if (!_validateRequiredFields(data)) {
            logMessage('⚠️ 자동 알람 데이터 필수 필드 누락: $data', level: LogLevel.warning);
            continue;
          }

          // AutoAlarm 객체 생성하여 올바른 다음 알람 시간 계산
          final autoAlarm = AutoAlarm.fromJson(data);

          // 다음 알람 시간 계산
          final nextAlarmTime = autoAlarm.getNextAlarmTime();
          if (nextAlarmTime == null) {
            logMessage(
              '⚠️ 자동 알람 다음 시간 계산 실패: ${autoAlarm.routeNo}',
              level: LogLevel.warning,
            );
            continue;
          }

          final alarm = alarm_model.AlarmData(
            id: autoAlarm.id,
            busNo: autoAlarm.routeNo,
            stationName: autoAlarm.stationName,
            remainingMinutes: 0,
            routeId: autoAlarm.routeId,
            scheduledTime: nextAlarmTime, // 올바른 다음 알람 시간 사용
            useTTS: autoAlarm.useTTS,
            isAutoAlarm: true,
            repeatDays: autoAlarm.repeatDays,
          );

          _autoAlarms.add(alarm);
          logMessage(
            '✅ 자동 알람 로드: ${alarm.busNo}, ${alarm.stationName}, 다음 시간: ${nextAlarmTime.toString()}',
          );
        } catch (e) {
          logMessage('❌ 자동 알람 파싱 오류: $e', level: LogLevel.error);
          continue;
        }
      }

      logMessage('✅ 자동 알람 로드 완료: ${_autoAlarms.length}개');
      notifyListeners();
    } catch (e) {
      logMessage('❌ 자동 알람 로드 실패: $e', level: LogLevel.error);
    }
  }

  bool _validateRequiredFields(Map<String, dynamic> data) {
    final requiredFields = [
      'routeNo',
      'stationId',
      'routeId',
      'stationName',
      'repeatDays',
    ];
    // scheduledTime 또는 hour/minute 중 하나는 필수
    if (data['scheduledTime'] == null &&
        (data['hour'] == null || data['minute'] == null)) {
      logMessage(
        '! 자동 알람 데이터 필수 필드 누락: scheduledTime 또는 hour/minute',
        level: LogLevel.error,
      );
      return false;
    }

    final missingFields = requiredFields
        .where(
          (field) =>
              data[field] == null ||
              (data[field] is String && data[field].isEmpty) ||
              (data[field] is List && (data[field] as List).isEmpty),
        )
        .toList();
    if (missingFields.isNotEmpty) {
      logMessage(
        '! 자동 알람 데이터 필수 필드 누락: [31m${missingFields.join(", ")}[0m',
        level: LogLevel.error,
      );
      return false;
    }
    return true;
  }

  Future<bool> startBusMonitoringService({
    required String stationId,
    required String stationName,
    required String routeId,
    required String busNo,
  }) async {
    try {
      // routeId가 비어있으면 기본값 설정
      final String effectiveRouteId =
          routeId.isEmpty ? '${busNo}_$stationName' : routeId;

      final Map<String, dynamic> arguments = {
        'stationId': stationId,
        'stationName': stationName,
        'routeId': effectiveRouteId,
        'busNo': busNo,
      };

      await _methodChannel?.invokeMethod(
        'startBusMonitoringService',
        arguments,
      );
      _isInTrackingMode = true;
      _trackedRouteId = effectiveRouteId;
      logMessage(
        '\ud83d\ude8c \ubc84\uc2a4 \ucd94\uc801 \uc2dc\uc791: $_trackedRouteId',
      );
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('🚌 버스 모니터링 서비스 시작 오류: $e');
      rethrow;
    }
  }

  Future<bool> stopBusMonitoringService() async {
    try {
      debugPrint('🚌 버스 모니터링 서비스 중지 시작');

      bool stopSuccess = false;

      // 1. 메서드 채널을 통한 중지 시도
      try {
        final result = await _methodChannel?.invokeMethod(
          'stopBusMonitoringService',
        );
        if (result == true) {
          stopSuccess = true;
          debugPrint('🚌 버스 모니터링 서비스 중지 성공 (result: $result)');
        } else {
          debugPrint('🚌 버스 모니터링 서비스 중지 실패 (result: $result)');
        }
      } catch (e) {
        debugPrint('🚌 버스 모니터링 서비스 중지 메서드 호출 오류: $e');
      }

      // 2. TTS 추적 중지 시도
      try {
        await _methodChannel?.invokeMethod('stopTtsTracking');
        debugPrint('🚌 TTS 추적 중지 성공');
      } catch (e) {
        debugPrint('🚌 TTS 추적 중지 오류: $e');
      }

      // 3. 알림 취소 시도
      try {
        await NotificationService().cancelOngoingTracking();
        debugPrint('🚌 진행 중인 추적 알림 취소 성공');

        // 모든 알림도 추가로 취소 시도
        await NotificationService().cancelAllNotifications();
        debugPrint('🚌 모든 알림 취소 성공');
      } catch (e) {
        debugPrint('🚌 알림 취소 시도 오류: $e');
      }

      // 4. 캐시 데이터 정리
      try {
        _processedNotifications.clear();
        debugPrint('🚌 처리된 알림 캐시 정리 완료');
      } catch (e) {
        debugPrint('🚌 캐시 정리 오류: $e');
      }

      // 5. 마지막으로 상태 변경
      _isInTrackingMode = false;
      _trackedRouteId = null;
      logMessage(
        '\ud83d\ude8c \ubc84\uc2a4 \ucd94\uc801 \uc911\uc9c0: \ucd94\uc801 \uc544\uc774\ub514 \ucd08\uae30\ud654',
      );
      notifyListeners();

      // 6. TTS로 알림 중지 알림
      try {
        // 이어폰 연결 시에만 TTS 발화
        await SimpleTTSHelper.speak("버스 추적이 중지되었습니다.", earphoneOnly: true);
      } catch (e) {
        debugPrint('🚌 TTS 알림 오류: $e');
      }

      debugPrint('🚌 모니터링 서비스 중지 완료, 추적 모드: $_isInTrackingMode');
      return stopSuccess || !_isInTrackingMode;
    } catch (e) {
      debugPrint('🚌 버스 모니터링 서비스 중지 오류: $e');

      // 오류 발생해도 강제로 상태 변경
      _isInTrackingMode = false;
      _processedNotifications.clear();
      notifyListeners();

      return false;
    }
  }

  CachedBusInfo? getCachedBusInfo(String busNo, String routeId) {
    final key = "${busNo}_$routeId";
    return _cachedBusInfo[key];
  }

  Map<String, dynamic>? getTrackingBusInfo() {
    if (!_isInTrackingMode) return null;

    // 해당 알람 정보가 있는 경우 우선 사용
    if (_activeAlarms.isNotEmpty) {
      final alarm = _activeAlarms.values.first;
      final key = "${alarm.busNo}_${alarm.routeId}";
      final cachedInfo = _cachedBusInfo[key];

      // 캐시된 실시간 정보가 있는 경우
      if (cachedInfo != null) {
        final remainingMinutes = cachedInfo.remainingMinutes;
        final isRecent =
            DateTime.now().difference(cachedInfo.lastUpdated).inMinutes < 10;

        if (isRecent) {
          return {
            'busNumber': alarm.busNo,
            'stationName': alarm.stationName,
            'remainingMinutes': remainingMinutes,
            'currentStation': cachedInfo.currentStation,
            'routeId': alarm.routeId,
          };
        }
      }

      // 캐시된 정보가 없거나 최신 정보가 아니면 알람에서 가져오기
      return {
        'busNumber': alarm.busNo,
        'stationName': alarm.stationName,
        'remainingMinutes': alarm.getCurrentArrivalMinutes(),
        'currentStation': alarm.currentStation ?? '',
        'routeId': alarm.routeId,
      };
    }

    // 알람이 없는 경우, 캡시된 정보에서 최신 것 찾기
    for (var entry in _cachedBusInfo.entries) {
      final key = entry.key;
      final cachedInfo = entry.value;

      // 현재 시간 기준으로 남은 시간 계산
      final remainingMinutes = cachedInfo.remainingMinutes;

      // 만약 정보가 10분 이내로 업데이트되었다면 유효한 정보로 간주
      final isRecent =
          DateTime.now().difference(cachedInfo.lastUpdated).inMinutes < 10;

      if (isRecent) {
        final parts = key.split('_');
        if (parts.isNotEmpty) {
          final busNumber = parts[0];
          final routeId = parts.length > 1 ? parts[1] : '';

          // 정류장 이름 찾기 (없는 경우 기본값)
          String stationName = '정류장';

          return {
            'busNumber': busNumber,
            'stationName': stationName,
            'remainingMinutes': remainingMinutes,
            'currentStation': cachedInfo.currentStation,
            'routeId': routeId,
          };
        }
      }
    }

    return null;
  }

  void updateBusInfoCache(
    String busNo,
    String routeId,
    dynamic busInfo,
    int remainingMinutes,
  ) {
    final cachedInfo = CachedBusInfo.fromBusInfo(
      busInfo: busInfo,
      busNumber: busNo,
      routeId: routeId,
    );
    final key = "${busNo}_$routeId";
    _cachedBusInfo[key] = cachedInfo;
    logMessage('🚌 버스 정보 캐시 업데이트: $busNo번, $remainingMinutes분 후');
  }

  Future<void> refreshAlarms() async {
    await loadAlarms();
    await loadAutoAlarms();
    notifyListeners();
  }

  void removeFromCacheBeforeCancel(
    String busNo,
    String stationName,
    String routeId,
  ) {
    final keysToRemove = <String>[];
    _activeAlarms.forEach((key, alarm) {
      if (alarm.busNo == busNo &&
          alarm.stationName == stationName &&
          alarm.routeId == routeId) {
        keysToRemove.add(key);
      }
    });

    for (var key in keysToRemove) {
      _activeAlarms.remove(key);
    }

    final cacheKey = "${busNo}_$routeId";
    _cachedBusInfo.remove(cacheKey);

    _autoAlarms.removeWhere(
      (alarm) =>
          alarm.busNo == busNo &&
          alarm.stationName == stationName &&
          alarm.routeId == routeId,
    );

    notifyListeners();
  }

  Future<List<DateTime>> _fetchHolidays(int year, int month) async {
    try {
      final String serviceKey = dotenv.env['SERVICE_KEY'] ?? '';
      if (serviceKey.isEmpty) {
        logMessage('❌ SERVICE_KEY가 설정되지 않았습니다', level: LogLevel.error);
        return [];
      }

      final String url =
          'http://apis.data.go.kr/B090041/openapi/service/SpcdeInfoService/getRestDeInfo'
          '?serviceKey=$serviceKey'
          '&solYear=$year'
          '&solMonth=${month.toString().padLeft(2, '0')}'
          '&numOfRows=100';

      try {
        final response = await http.get(Uri.parse(url));
        if (response.statusCode == 200) {
          try {
            final holidays = <DateTime>[];
            final xmlDoc = xml.XmlDocument.parse(response.body);
            final items = xmlDoc.findAllElements('item');

            for (var item in items) {
              final isHoliday =
                  item.findElements('isHoliday').firstOrNull?.innerText;
              if (isHoliday == 'Y') {
                final locdate =
                    item.findElements('locdate').firstOrNull?.innerText;
                if (locdate != null && locdate.length == 8) {
                  final year = int.parse(locdate.substring(0, 4));
                  final month = int.parse(locdate.substring(4, 6));
                  final day = int.parse(locdate.substring(6, 8));
                  holidays.add(DateTime(year, month, day));
                }
              }
            }

            logMessage('✅ 공휴일 목록 ($year-$month): ${holidays.length}개 공휴일 발견');
            return holidays;
          } catch (e) {
            logMessage('❌ XML 파싱 오류: $e', level: LogLevel.error);
            return [];
          }
        } else {
          logMessage(
            '❌ 공휴일 API 응답 오류: ${response.statusCode}',
            level: LogLevel.error,
          );
          return [];
        }
      } catch (e) {
        logMessage('❌ 공휴일 API 호출 오류: $e', level: LogLevel.error);
        return [];
      }
    } catch (e) {
      logMessage('❌ 공휴일 조회 오류: $e', level: LogLevel.error);
      return [];
    }
  }

  // 자동 알람 체크 메서드 수정 (올바른 시간 계산 및 실행)
  Future<void> _checkAutoAlarms() async {
    try {
      // 자동 알람 설정이 비활성화되어 있으면 실행하지 않음
      final settingsService = SettingsService();
      if (!settingsService.useAutoAlarm || !_autoAlarmEnabled) {
        logMessage(
          '⚠️ 자동 알람이 설정에서 비활성화되어 있거나 수동으로 중지되었습니다.',
          level: LogLevel.warning,
        );
        return;
      }

      final now = DateTime.now();

      // 리스트를 복사해서 순회하여 Concurrent modification 방지
      final alarmsCopy = List<alarm_model.AlarmData>.from(_autoAlarms);

      for (var alarm in alarmsCopy) {
        // 현재 시간과 알람 시간 비교 (정확한 시간 매칭 + 오차 허용)
        final alarmTime = alarm.scheduledTime;
        final timeDifference = alarmTime.difference(now);

        // 정확한 시간과 분 매칭 (초는 무시)
        bool isTargetTime =
            now.hour == alarmTime.hour && now.minute == alarmTime.minute;

        // 알람 시간 범위 체크 (해당 분의 0~59초 내에 있는지)
        bool isWithinMinute =
            timeDifference.inSeconds >= -59 && timeDifference.inSeconds <= 0;

        logMessage(
          '🕒 알람 시간 체크: ${alarm.busNo}번 - 현재: ${now.hour}:${now.minute}:${now.second}, 알람: ${alarmTime.hour}:${alarmTime.minute}, 차이: ${timeDifference.inSeconds}초',
          level: LogLevel.debug,
        );

        if (isTargetTime && isWithinMinute) {
          final alarmKey =
              "${alarm.busNo}_${alarm.stationName}_${alarm.routeId}";

          // 중복 실행 방지 체크 (같은 분에 이미 실행되었는지 확인)
          final executionKey =
              "${alarmKey}_${alarmTime.hour}:${alarmTime.minute}";
          if (_executedAlarms.containsKey(executionKey)) {
            final lastExecution = _executedAlarms[executionKey]!;
            final sameMinute = lastExecution.hour == now.hour &&
                lastExecution.minute == now.minute;
            if (sameMinute) {
              logMessage(
                '⏭️ 알람 중복 실행 방지: ${alarm.busNo}번 - 이미 ${lastExecution.hour}:${lastExecution.minute}에 실행됨',
                level: LogLevel.warning,
              );
              continue;
            }
          }

          logMessage(
            '✅ 알람 실행 조건 만족: ${alarm.busNo}번 - 시간: ${alarmTime.hour}:${alarmTime.minute}, 차이: ${timeDifference.inSeconds}초',
            level: LogLevel.info,
          );

          // 수동으로 중지된 알람인지 확인 (다음날 자정에 자동 해제)
          if (_manuallyStoppedAlarms.contains(alarmKey)) {
            final stoppedTime = _manuallyStoppedTimestamps[alarmKey];
            if (stoppedTime != null) {
              // 중지된 날짜와 현재 날짜 비교
              final stoppedDate = DateTime(
                stoppedTime.year,
                stoppedTime.month,
                stoppedTime.day,
              );
              final currentDate = DateTime(now.year, now.month, now.day);

              if (currentDate.isAfter(stoppedDate)) {
                // 다음날이 되면 수동 중지 목록에서 제거
                _manuallyStoppedAlarms.remove(alarmKey);
                _manuallyStoppedTimestamps.remove(alarmKey);
                logMessage(
                  '✅ 수동 중지 알람 자동 해제 (다음날 도래): ${alarm.busNo}번, ${alarm.stationName}',
                  level: LogLevel.info,
                );
              } else {
                logMessage(
                  '⚠️ 자동 알람 스킵 (오늘 수동으로 중지됨): ${alarm.busNo}번, ${alarm.stationName}',
                  level: LogLevel.warning,
                );
                continue;
              }
            } else {
              // 타임스탬프가 없으면 목록에서 제거
              _manuallyStoppedAlarms.remove(alarmKey);
            }
          }

          // 이미 추적 중인 알람인지 확인
          if (_activeAlarms.containsKey(alarmKey)) {
            logMessage(
              '⚠️ 자동 알람 스킵 (이미 추적 중): ${alarm.busNo}번, ${alarm.stationName}',
              level: LogLevel.warning,
            );
            continue;
          }

          logMessage(
            '⚡ 자동 알람 실행: ${alarm.busNo}번, 예정 시간: ${alarmTime.toString()}, 현재 시간: ${now.toString()}',
            level: LogLevel.info,
          );

          // 올바른 stationId 가져오기 (DB 실패 시 매핑 사용)
          String effectiveStationId = _getStationIdFromName(
            alarm.stationName,
            alarm.routeId,
          );

          // DB를 통한 추가 보정 시도 (선택사항)
          try {
            final dbHelper = DatabaseHelper();
            final resolvedStationId = await dbHelper.getStationIdFromWincId(
              alarm.stationName,
            );
            if (resolvedStationId != null && resolvedStationId.isNotEmpty) {
              effectiveStationId = resolvedStationId;
              logMessage(
                '✅ 자동 알람 DB stationId 보정: ${alarm.stationName} → $effectiveStationId',
                level: LogLevel.debug,
              );
            } else {
              logMessage(
                '⚠️ DB stationId 보정 실패, 매핑값 사용: ${alarm.stationName} → $effectiveStationId',
                level: LogLevel.debug,
              );
            }
          } catch (e) {
            logMessage(
              '❌ DB stationId 보정 중 오류, 매핑값 사용: $e → $effectiveStationId',
              level: LogLevel.warning,
            );
          }

          // AutoAlarm 객체로 변환
          final autoAlarm = AutoAlarm(
            id: alarm.id.toString(),
            routeNo: alarm.busNo,
            stationName: alarm.stationName,
            stationId: effectiveStationId, // 올바른 stationId 사용
            routeId: alarm.routeId,
            hour: alarmTime.hour,
            minute: alarmTime.minute,
            repeatDays: (alarm.repeatDays?.isNotEmpty ?? false)
                ? alarm.repeatDays!
                : [now.weekday], // 반복 요일 사용
            useTTS: alarm.useTTS,
            isActive: true,
          );

          // 실행 기록 저장 (중복 실행 방지)
          _executedAlarms[executionKey] = now;

          // 즉시 실행하고 지속적인 모니터링 시작
          await _startContinuousAutoAlarm(autoAlarm);

          // 실행된 알람을 제거하고 다음 알람 스케줄링
          _autoAlarms.removeWhere((a) => a.id == alarm.id);

          // 다음 알람 시간 계산하여 다시 추가
          final nextAlarmTime = autoAlarm.getNextAlarmTime();
          if (nextAlarmTime != null) {
            final nextAlarm = alarm_model.AlarmData(
              id: alarm.id,
              busNo: alarm.busNo,
              stationName: alarm.stationName,
              remainingMinutes: 0,
              routeId: alarm.routeId,
              scheduledTime: nextAlarmTime,
              useTTS: alarm.useTTS,
              isAutoAlarm: true,
              repeatDays: alarm.repeatDays ?? [],
            );
            _autoAlarms.add(nextAlarm);
            logMessage(
              '✅ 다음 자동 알람 스케줄링: ${alarm.busNo}번, 다음 시간: ${nextAlarmTime.toString()}',
            );
          }

          await _saveAutoAlarms();
          logMessage('✅ 자동 알람 실행 완료: ${alarm.busNo}번', level: LogLevel.info);
        }
      }
    } catch (e) {
      logMessage('❌ 자동 알람 체크 오류: $e', level: LogLevel.error);
    }
  }

  // 지속적인 자동 알람 시작 메서드 추가
  Future<void> _startContinuousAutoAlarm(AutoAlarm alarm) async {
    try {
      logMessage(
        '⚡ 지속적인 자동 알람 시작: ${alarm.routeNo}번, ${alarm.stationName}',
        level: LogLevel.info,
      );

      // 버스 모니터링 서비스 시작
      await startBusMonitoringService(
        routeId: alarm.routeId,
        stationId: alarm.stationId,
        busNo: alarm.routeNo,
        stationName: alarm.stationName,
      );

      // 정기적인 업데이트 타이머 시작 (30초마다)
      _refreshTimer?.cancel();
      _refreshTimer = Timer.periodic(const Duration(seconds: 30), (
        timer,
      ) async {
        if (!_isInTrackingMode) {
          timer.cancel();
          return;
        }

        try {
          // 실시간 버스 정보 업데이트
          await refreshAutoAlarmBusInfo(alarm);

          // 캐시된 정보 가져오기
          final cacheKey = "${alarm.routeNo}_${alarm.routeId}";
          final cachedInfo = _cachedBusInfo[cacheKey];

          final remainingMinutes = cachedInfo?.remainingMinutes ?? 0;
          final currentStation = cachedInfo?.currentStation ?? '정보 업데이트 중';

          // 지속적인 알림도 BusAlertService에서 처리 - Flutter 중복 알림 제거
          // BusAlertService가 이미 포그라운드 알림을 30초마다 업데이트함

          logMessage(
            '🔄 자동 알람 업데이트: ${alarm.routeNo}번, $remainingMinutes분 후, 현재: $currentStation',
            level: LogLevel.info,
          );

          // TTS 발화 (1분마다)
          if (timer.tick % 2 == 0) {
            // 1분마다 (30초 * 2)
            if (alarm.useTTS) {
              try {
                await SimpleTTSHelper.speakBusAlert(
                  busNo: alarm.routeNo,
                  stationName: alarm.stationName,
                  remainingMinutes: remainingMinutes,
                  currentStation: currentStation,
                  isAutoAlarm: true, // 🔊 자동 알람 플래그 전달 - 강제 스피커 모드
                );

                logMessage(
                  '🔊 자동 알람 TTS 반복 발화: ${alarm.routeNo}번, $remainingMinutes분 후',
                  level: LogLevel.info,
                );
              } catch (e) {
                logMessage('❌ 자동 알람 TTS 반복 발화 오류: $e', level: LogLevel.error);
              }
            }
          }
        } catch (e) {
          logMessage('❌ 자동 알람 업데이트 오류: $e', level: LogLevel.error);
        }
      });

      // 초기 실행
      await _executeAutoAlarmImmediately(alarm);

      logMessage('✅ 지속적인 자동 알람 시작 완료: ${alarm.routeNo}번', level: LogLevel.info);
    } catch (e) {
      logMessage('❌ 지속적인 자동 알람 시작 오류: $e', level: LogLevel.error);
    }
  }

  Future<List<DateTime>> getHolidays(int year, int month) async {
    return _fetchHolidays(year, month);
  }

  Future<void> _scheduleAutoAlarm(
    AutoAlarm alarm,
    DateTime scheduledTime,
  ) async {
    // 필수 파라미터 검증
    if (!_validateRequiredFields(alarm.toJson())) {
      logMessage(
        '❌ 필수 파라미터 누락으로 자동 알람 예약 거부: ${alarm.toJson()}',
        level: LogLevel.error,
      );
      return;
    }
    try {
      final now = DateTime.now();
      final String uniqueAlarmId = "auto_alarm_${alarm.id}";
      final initialDelay = scheduledTime.difference(now);

      // 너무 먼 미래의 알람은 최대 3일로 제한
      final actualDelay =
          initialDelay.inDays > 3 ? const Duration(days: 3) : initialDelay;

      // 음수 딜레이는 즉시 실행
      final executionDelay =
          actualDelay.isNegative ? Duration.zero : actualDelay;

      

      // 이제 WorkManager 대신 네이티브 AlarmManager를 통해 스케줄링
      // 즉시 실행 조건은 Flutter에서 먼저 판단하여 네이티브에 전달

      final bool isImmediate =
          executionDelay.isNegative || executionDelay.inSeconds <= 30;

      // 네이티브 AlarmManager 스케줄링 요청
      await _methodChannel?.invokeMethod('scheduleNativeAlarm', {
        'busNo': alarm.routeNo,
        'stationName': alarm.stationName,
        'routeId': alarm.routeId,
        'stationId': alarm.stationId, // alarm 객체에서 stationId 사용
        'useTTS': alarm.useTTS,
        'hour': scheduledTime.hour,
        'minute': scheduledTime.minute,
        'repeatDays': alarm.repeatDays, // 요일 반복 정보 전달
        'isImmediate': isImmediate, // 즉시 실행 여부 전달 (네이티브에서 활용)
      });

      logMessage(
        '✅ 네이티브 AlarmManager 스케줄링 요청 완료: ${alarm.routeNo} at $scheduledTime, 즉시 실행: $isImmediate',
      );

      // SharedPreferences에 작업 등록 정보 저장 (검증용)
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'last_scheduled_alarm_$uniqueAlarmId',
        jsonEncode({
          'workId': 'native_alarm_$uniqueAlarmId', // 네이티브 알람임을 표시
          'busNo': alarm.routeNo,
          'stationName': alarm.stationName,
          'scheduledTime': scheduledTime.toIso8601String(),
          'registeredAt': now.toIso8601String(),
        }),
      );

      logMessage(
        '✅ 자동 알람 예약 성공: ${alarm.routeNo} at $scheduledTime (${executionDelay.inMinutes}분 후), 작업 ID: native_alarm_$uniqueAlarmId',
      );

      // 30초 후 백업 알람 등록 (즉시 실행되지 않은 경우만)
      if (executionDelay.inSeconds > 30) {
        _scheduleBackupAlarm(alarm, uniqueAlarmId.hashCode, scheduledTime);
        logMessage(
          '✅ 백업 알람 등록: ${alarm.routeNo}번, ${executionDelay.inMinutes}분 후 실행',
          level: LogLevel.info,
        );
      }
    } catch (e) {
      logMessage('❌ 자동 알람 예약 오류: $e', level: LogLevel.error);
      // 오류 발생 시 앱 내 로컬 알림으로 예약 시도 (이 부분은 유지 또는 개선 필요)
      _scheduleLocalBackupAlarm(alarm, scheduledTime);
    }
  }

  // 즉시 실행 자동 알람 메서드 추가
  Future<void> _executeAutoAlarmImmediately(AutoAlarm alarm) async {
    try {
      // 🛑 사용자가 수동으로 중지한 직후인지 확인 (재시작 방지)
      if (_userManuallyStopped) {
        final now = DateTime.now().millisecondsSinceEpoch;
        final timeSinceStop = now - _lastManualStopTime;
        if (timeSinceStop < _restartPreventionDuration) {
          logMessage(
            '🛑 사용자가 ${(timeSinceStop / 1000).toInt()}초 전에 수동 중지했음 - 자동 알람 실행 거부: ${alarm.routeNo}번',
            level: LogLevel.warning,
          );
          return;
        } else {
          // 30초가 지났으면 플래그 해제
          _userManuallyStopped = false;
          _lastManualStopTime = 0;
          logMessage(
            '✅ Flutter 재시작 방지 기간 만료 - 자동 알람 실행 허용: ${alarm.routeNo}번',
            level: LogLevel.info,
          );
        }
      }

      // 자동 알람 설정이 비활성화되어 있으면 실행하지 않음
      final settingsService = SettingsService();
      if (!settingsService.useAutoAlarm) {
        logMessage(
          '⚠️ 자동 알람이 설정에서 비활성화되어 있어 실행하지 않습니다: ${alarm.routeNo}번',
          level: LogLevel.warning,
        );
        return;
      }

      logMessage(
        '⚡ 즉시 자동 알람 실행: ${alarm.routeNo}번, ${alarm.stationName}',
        level: LogLevel.info,
      );

      // 실시간 버스 정보 가져오기
      await refreshAutoAlarmBusInfo(alarm);

      // 캐시된 정보 가져오기
      final cacheKey = "${alarm.routeNo}_${alarm.routeId}";
      final cachedInfo = _cachedBusInfo[cacheKey];

      final remainingMinutes = cachedInfo?.remainingMinutes ?? 0;
      final currentStation = cachedInfo?.currentStation ?? '정보 업데이트 중';

      // 자동 알람에서는 Flutter 알림 생략 - BusAlertService에서 처리
      // Flutter 중복 알림 제거: BusAlertService가 포그라운드 알림을 처리함

      // 자동 알람 실행 시 activeAlarms에도 추가하여 UI에 표시
      final alarmData = alarm_model.AlarmData(
        id: alarm.id,
        busNo: alarm.routeNo,
        stationName: alarm.stationName,
        remainingMinutes: remainingMinutes,
        routeId: alarm.routeId,
        scheduledTime: DateTime.now().add(
          Duration(minutes: remainingMinutes.clamp(0, 60)),
        ),
        currentStation: currentStation,
        useTTS: alarm.useTTS,
        isAutoAlarm: true,
      );

      // activeAlarms에 추가하여 ActiveAlarmPanel에서 표시되도록 함
      final alarmKey = "${alarm.routeNo}_${alarm.stationName}_${alarm.routeId}";
      _activeAlarms[alarmKey] = alarmData;
      await _saveAlarms();

      logMessage(
        '✅ 자동 알람을 activeAlarms에 추가: ${alarm.routeNo}번 ($remainingMinutes분 후)',
        level: LogLevel.info,
      );

      // 즉시 실행 자동 알람에서는 TTS도 즉시 실행 (정확한 시간 보장)
      if (alarm.useTTS) {
        try {
          await SimpleTTSHelper.speakBusAlert(
            busNo: alarm.routeNo,
            stationName: alarm.stationName,
            remainingMinutes: remainingMinutes,
            currentStation: currentStation,
            isAutoAlarm: true, // 🔊 자동 알람 플래그 전달 - 강제 스피커 모드
          );
          logMessage('🔊 즉시 자동 알람 TTS 발화 완료 (강제 스피커 모드)', level: LogLevel.info);
        } catch (e) {
          logMessage('❌ 즉시 자동 알람 TTS 발화 오류: $e', level: LogLevel.error);
        }
      }

      logMessage('✅ 즉시 자동 알람 실행 완료: ${alarm.routeNo}번', level: LogLevel.info);

      // UI 업데이트
      notifyListeners();
    } catch (e) {
      logMessage('❌ 즉시 자동 알람 실행 오류: $e', level: LogLevel.error);
    }
  }

  // 로컬 백업 알람 등록 함수
  Future<void> _scheduleLocalBackupAlarm(
    AutoAlarm alarm,
    DateTime scheduledTime,
  ) async {
    try {
      logMessage(
        '⏰ 로컬 백업 알람 등록 시도: ${alarm.routeNo}, ${alarm.stationName}',
        level: LogLevel.debug,
      );

      // TTS 및 알림으로 사용자에게 정보 제공
      try {
        await SimpleTTSHelper.speak(
          "${alarm.routeNo}번 버스 자동 알람 예약에 문제가 발생했습니다. 앱을 다시 실행해 주세요.",
        );
      } catch (e) {
        logMessage('🔊 TTS 알림 실패: $e', level: LogLevel.error);
      }

      // 메인 앱이 실행될 때 처리할 수 있도록 정보 저장
      final prefs = await SharedPreferences.getInstance();
      final alarmInfo = {
        'routeNo': alarm.routeNo,
        'stationName': alarm.stationName,
        'scheduledTime': scheduledTime.toIso8601String(),
        'registeredAt': DateTime.now().toIso8601String(),
        'hasSchedulingError': true,
      };

      await prefs.setString('alarm_scheduling_error', jsonEncode(alarmInfo));
      await prefs.setBool('has_alarm_scheduling_error', true);

      logMessage('⏰ 로컬 백업 알람 정보 저장 완료', level: LogLevel.debug);
    } catch (e) {
      logMessage('❌ 로컬 백업 알람 등록 실패: $e', level: LogLevel.error);
    }
  }

  // 백업 알람 등록 함수 추가
  Future<void> _scheduleBackupAlarm(
    AutoAlarm alarm,
    int id,
    DateTime scheduledTime,
  ) async {
    try {
      final backupTime = scheduledTime.subtract(const Duration(minutes: 5));
      final now = DateTime.now();
      if (backupTime.isBefore(now)) {
        logMessage('⚠️ 백업 알람 시간($backupTime)이 현재($now)보다 빠릅니다. 백업 알람 등록 취소.');
        return; // 이미 지난 시간이면 등록 취소
      }

      

      logMessage('✅ 네이티브 백업 알람 스케줄링 요청 완료: ${alarm.routeNo} at $backupTime');
    } catch (e) {
      logMessage('❌ 백업 알람 예약 오류: $e', level: LogLevel.error);
    }
  }

  Future<void> _saveAutoAlarms() async {
    try {
      logMessage('🔄 자동 알람 저장 시작...');
      final prefs = await SharedPreferences.getInstance();
      final List<String> alarms = _autoAlarms.map((alarm) {
        final autoAlarm = AutoAlarm(
          id: alarm.id,
          routeNo: alarm.busNo,
          stationName: alarm.stationName,
          stationId: _getStationIdFromName(
            alarm.stationName,
            alarm.routeId,
          ),
          routeId: alarm.routeId,
          hour: alarm.scheduledTime.hour,
          minute: alarm.scheduledTime.minute,
          repeatDays: alarm.repeatDays ?? [],
          useTTS: alarm.useTTS,
          isActive: true,
        );

        final json = autoAlarm.toJson();
        // scheduledTime을 toIso8601String()으로 변환하여 저장
        json['scheduledTime'] = alarm.scheduledTime.toIso8601String();
        final jsonString = jsonEncode(json);

        // 각 알람의 데이터 로깅
        logMessage('📝 알람 데이터 변환: ${alarm.busNo}번 버스');
        logMessage('  - ID: ${autoAlarm.id}');
        logMessage('  - 시간: ${autoAlarm.hour}:${autoAlarm.minute}');
        logMessage(
          '  - 정류장: ${autoAlarm.stationName} (${autoAlarm.stationId})',
        );
        logMessage(
          '  - 반복: ${autoAlarm.repeatDays.map((d) => [
                '월',
                '화',
                '수',
                '목',
                '금',
                '토',
                '일'
              ][d - 1]).join(", ")}',
        );
        logMessage('  - JSON: $jsonString');

        return jsonString;
      }).toList();

      // 저장 전 데이터 확인
      logMessage('📊 저장할 알람 수: ${alarms.length}개');

      // SharedPreferences에 저장
      await prefs.setStringList('auto_alarms', alarms);

      // 저장 후 확인
      final savedAlarms = prefs.getStringList('auto_alarms') ?? [];
      logMessage('✅ 자동 알람 저장 완료');
      logMessage('  - 저장된 알람 수: ${savedAlarms.length}개');
      if (savedAlarms.isNotEmpty) {
        final firstAlarm = jsonDecode(savedAlarms.first);
        logMessage('  - 첫 번째 알람 정보:');
        logMessage('    • 버스: ${firstAlarm['routeNo']}');
        logMessage('    • 시간: ${firstAlarm['scheduledTime']}');
        logMessage(
          '    • 반복: ${(firstAlarm['repeatDays'] as List).map((d) => [
                '월',
                '화',
                '수',
                '목',
                '금',
                '토',
                '일'
              ][d - 1]).join(", ")}',
        );
      }
    } catch (e) {
      logMessage('❌ 자동 알람 저장 오류: $e', level: LogLevel.error);
      logMessage('  - 스택 트레이스: ${e is Error ? e.stackTrace : "없음"}');
    }
  }

  /// 알람 시작
  Future<void> startAlarm(
    String busNo,
    String stationName,
    int remainingMinutes, {
    bool isAutoAlarm = false,
  }) async {
    try {
      // TTS 발화
      if (_useTTS) {
        await SimpleTTSHelper.speakBusAlert(
          busNo: busNo,
          stationName: stationName,
          remainingMinutes: remainingMinutes,
          earphoneOnly: !isAutoAlarm, // 일반 알람은 이어폰 전용, 자동 알람은 스피커 허용
          isAutoAlarm: isAutoAlarm, // 🔊 자동 알람 플래그 전달
        );
      }

      // 알람 해제 시에도 설정된 모드 유지
      await _notificationService.showBusArrivingSoon(
        busNo: busNo,
        stationName: stationName,
      );
    } catch (e) {
      logMessage('❌ 알람 시작 오류: $e', level: LogLevel.error);
    }
  }

  /// 자동 알람 중지 메서드 추가
  Future<bool> stopAutoAlarm(
    String busNo,
    String stationName,
    String routeId,
  ) async {
    try {
      logMessage('📋 자동 알람 중지 요청: $busNo번, $stationName', level: LogLevel.info);

      // 수동 중지 알람 목록에 추가 (재시작 방지)
      final alarmKey = "${busNo}_${stationName}_$routeId";
      _manuallyStoppedAlarms.add(alarmKey);
      _manuallyStoppedTimestamps[alarmKey] = DateTime.now();
      logMessage('🚫 수동 중지 알람 추가: $alarmKey', level: LogLevel.info);

      // 새로고침 타이머 중지
      _refreshTimer?.cancel();
      _refreshTimer = null;

      // 버스 모니터링 서비스 중지
      await _notificationService.cancelOngoingTracking();

      // 알림 취소
      await _notificationService.cancelOngoingTracking();

      // 자동 알람 목록에서 제거
      _autoAlarms.removeWhere(
        (alarm) =>
            alarm.busNo == busNo &&
            alarm.stationName == stationName &&
            alarm.routeId == routeId,
      );

      // activeAlarms에서도 제거
      _activeAlarms.remove(alarmKey);

      await _saveAutoAlarms();
      await _saveAlarms();

      // TTS 중지 알림
      try {
        await SimpleTTSHelper.speak(
          "$busNo번 버스 자동 알람이 중지되었습니다.",
          force: true,
          earphoneOnly: false,
        );
      } catch (e) {
        logMessage('❌ TTS 중지 알림 오류: $e', level: LogLevel.error);
      }

      logMessage('✅ 자동 알람 중지 완료: $busNo번', level: LogLevel.info);

      notifyListeners();
      return true;
    } catch (e) {
      logMessage('❌ 자동 알람 중지 오류: $e', level: LogLevel.error);
      return false;
    }
  }

  /// 알람 해제
  Future<void> stopAlarm(
    String busNo,
    String stationName, {
    bool isAutoAlarm = false,
  }) async {
    try {
      // TTS로 알람 해제 안내
      if (_useTTS) {
        await SimpleTTSHelper.speak(
          "$busNo번 버스 알람이 해제되었습니다.",
          earphoneOnly: !isAutoAlarm, // 일반 알람은 이어폰 전용, 자동 알람은 설정된 모드 사용
        );
      }

      // 알림 제거
      await _notificationService.cancelOngoingTracking();
    } catch (e) {
      logMessage('❌ 알람 해제 오류: $e', level: LogLevel.error);
    }
  }

  bool hasAlarm(String busNo, String stationName, String routeId) {
    // 일반 승차 알람만 확인 (자동 알람 제외)
    final bool hasRegularAlarm = _activeAlarms.values.any(
      (alarm) =>
          alarm.busNo == busNo &&
          alarm.stationName == stationName &&
          alarm.routeId == routeId,
    );

    // 자동 알람 여부 확인
    final bool hasAutoAlarm = _autoAlarms.any(
      (alarm) =>
          alarm.busNo == busNo &&
          alarm.stationName == stationName &&
          alarm.routeId == routeId,
    );

    // 추적 중인지 여부 확인
    final bool isTracking = isInTrackingMode;
    bool isThisBusTracked = false;
    if (isTracking && _trackedRouteId != null) {
      // 현재 추적 중인 버스와 동일한지 확인
      isThisBusTracked = _trackedRouteId == routeId;
    }

    // 자동 알람이 있으면 승차 알람은 비활성화
    return hasRegularAlarm &&
        !hasAutoAlarm &&
        (!isTracking || isThisBusTracked);
  }

  bool hasAutoAlarm(String busNo, String stationName, String routeId) {
    return _autoAlarms.any(
      (alarm) =>
          alarm.busNo == busNo &&
          alarm.stationName == stationName &&
          alarm.routeId == routeId,
    );
  }

  alarm_model.AlarmData? getAutoAlarm(
    String busNo,
    String stationName,
    String routeId,
  ) {
    try {
      return _autoAlarms.firstWhere(
        (alarm) =>
            alarm.busNo == busNo &&
            alarm.stationName == stationName &&
            alarm.routeId == routeId,
      );
    } catch (e) {
      debugPrint('자동 알람을 찾을 수 없음: $busNo, $stationName, $routeId');
      return null;
    }
  }

  alarm_model.AlarmData? findAlarm(
    String busNo,
    String stationName,
    String routeId,
  ) {
    try {
      return _activeAlarms.values.firstWhere(
        (alarm) =>
            alarm.busNo == busNo &&
            alarm.stationName == stationName &&
            alarm.routeId == routeId,
      );
    } catch (e) {
      try {
        return _autoAlarms.firstWhere(
          (alarm) =>
              alarm.busNo == busNo &&
              alarm.stationName == stationName &&
              alarm.routeId == routeId,
        );
      } catch (e) {
        return null;
      }
    }
  }

  Future<bool> setOneTimeAlarm(
    String busNo,
    String stationName,
    int remainingMinutes, {
    String routeId = '',
    bool useTTS = true,
    bool isImmediateAlarm = true,
    String? currentStation,
  }) async {
    try {
      logMessage(
        '🚌 일반 알람 설정 시작: $busNo번 버스, $stationName, $remainingMinutes분',
      );

      final id = "${busNo}_${stationName}_$routeId";

      // 알람 데이터 생성
      final alarmData = alarm_model.AlarmData(
        id: id,
        busNo: busNo,
        stationName: stationName,
        remainingMinutes: remainingMinutes,
        routeId: routeId,
        scheduledTime: DateTime.now().add(Duration(minutes: remainingMinutes)),
        currentStation: currentStation,
        useTTS: useTTS,
        isAutoAlarm: false,
      );

      // 알람 저장 (키는 알람의 고유 ID 문자열 사용)
      _activeAlarms[alarmData.id] = alarmData;
      await _saveAlarms();

      // 설정된 알람 볼륨 가져오기
      final settingsService = SettingsService();
      await settingsService.initialize();
      final volume = settingsService.autoAlarmVolume;

      // 🔔 간단한 일회성 알림만 표시 (진행중 추적 알림 비활성화)
      try {
        // 알림 ID는 고유 ID의 해시코드를 사용
        await _notificationService.showNotification(
          id: alarmData.id.hashCode,
          busNo: busNo,
          stationName: stationName,
          remainingMinutes: remainingMinutes,
          currentStation: currentStation ?? '정보 없음',
          routeId: routeId,
          isAutoAlarm: false, // 일반 알람
          isOngoing: false, // 🔔 간단한 일회성 알림만 표시 (진행중 추적 노티 완전 비활성화)
        );
        logMessage('✅ 일반 알람 간단한 알림 표시 완료: $busNo번', level: LogLevel.info);
      } catch (e) {
        logMessage('❌ 일반 알람 알림 표시 오류: $e', level: LogLevel.error);
      }

      // TTS 알림 시작 (설정된 경우 - 일반 알람)
      if (useTTS) {
        try {
          await SimpleTTSHelper.initialize();
          await SimpleTTSHelper.setVolume(volume); // 볼륨 설정

          logMessage(
            '🔊 일반 알람 TTS 발화 시도: $busNo번 버스, $remainingMinutes분 후',
            level: LogLevel.info,
          );

          // 🎧 일반 알람은 이어폰 연결 시에만 TTS 발화 (earphoneOnly: true)
          final success = await SimpleTTSHelper.speak(
            "$busNo번 버스가 약 $remainingMinutes분 후 도착 예정입니다.",
            earphoneOnly: true, // 🎧 일반 알람은 이어폰 전용 모드 - 이어폰 연결 시에만 TTS 발화
          );

          if (success) {
            logMessage(
              '✅ 일반 알람 TTS 발화 완료 (볼륨: ${volume * 100}%)',
              level: LogLevel.info,
            );
          } else {
            logMessage(
              '❌ 일반 알람 TTS 발화 실패 (이어폰 미연결일 수 있음)',
              level: LogLevel.warning,
            );
          }
        } catch (e) {
          logMessage('❌ 일반 알람 TTS 발화 오류: $e', level: LogLevel.error);
        }
      }

      logMessage('✅ 알람 설정 완료: $busNo번 버스');
      notifyListeners();
      return true;
    } catch (e) {
      logMessage('❌ 알람 설정 오류: $e', level: LogLevel.error);
      return false;
    }
  }

  Future<void> _saveAlarms() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> alarms = _activeAlarms.values
          .map((alarm) => jsonEncode(alarm.toJson()))
          .toList();
      await prefs.setStringList('alarms', alarms);
      logMessage('✅ 알람 저장 완료: ${alarms.length}개');
    } catch (e) {
      logMessage('❌ 알람 저장 오류: $e', level: LogLevel.error);
    }
  }

  // 특정 추적 중지 메서드 추가
  Future<bool> stopSpecificTracking({
    required String busNo,
    required String stationName,
    required String routeId,
  }) async {
    try {
      logMessage('🐛 [DEBUG] 특정 추적 중지 요청: $busNo번 버스, $stationName, $routeId');

      // 1. 네이티브 서비스에 특정 추적 중지 요청
      await _methodChannel?.invokeMethod('stopSpecificTracking', {
        'busNo': busNo,
        'routeId': routeId,
        'stationName': stationName,
      });

      // 2. Flutter 측 상태 업데이트
      await cancelAlarmByRoute(busNo, stationName, routeId);

      logMessage('🐛 [DEBUG] ✅ 특정 추적 중지 완료: $busNo번 버스');
      return true;
    } catch (e) {
      logMessage('❌ [ERROR] 특정 추적 중지 실패: $e', level: LogLevel.error);
      return false;
    }
  }

  // 모든 추적 중지 메서드 개선
  Future<bool> stopAllTracking() async {
    try {
      logMessage('🐛 [DEBUG] 모든 추적 중지 요청: ${_activeAlarms.length}개');

      // 1. 네이티브 서비스 완전 중지
      await _notificationService.cancelOngoingTracking();

      // 2. TTS 추적 중지
      try {
        await _methodChannel?.invokeMethod('stopTtsTracking');
        logMessage('✅ stopTtsTracking 호출 완료', level: LogLevel.debug);
      } catch (e) {
        logMessage('⚠️ stopTtsTracking 실패 (무시): $e', level: LogLevel.warning);
      }

      // 3. 모든 알림 취소
      try {
        await _notificationService.cancelAllNotifications();
        logMessage('✅ cancelAllNotifications 호출 완료', level: LogLevel.debug);
      } catch (e) {
        logMessage(
          '⚠️ cancelAllNotifications 실패 (무시): $e',
          level: LogLevel.warning,
        );
      }

      // 4. Flutter 측 상태 완전 정리
      _activeAlarms.clear();
      _cachedBusInfo.clear();
      _isInTrackingMode = false;
      _trackedRouteId = null;
      _processedNotifications.clear();

      // 5. 타이머 정리
      _refreshTimer?.cancel();
      _refreshTimer = null;

      // 6. 상태 저장 및 UI 업데이트
      await _saveAlarms();
      notifyListeners();

      logMessage('🐛 [DEBUG] ✅ 모든 추적 중지 완료');
      return true;
    } catch (e) {
      logMessage('❌ [ERROR] 모든 추적 중지 실패: $e', level: LogLevel.error);
      return false;
    }
  }

  /// 알람 취소 메서드
  Future<bool> cancelAlarmByRoute(
    String busNo,
    String stationName,
    String routeId,
  ) async {
    logMessage(
      '🚌 [Request] 알람 취소 요청: $busNo번 버스, $stationName, routeId: $routeId',
    );

    final String alarmKey = "${busNo}_${stationName}_$routeId";
    final String cacheKey = "${busNo}_$routeId";
    bool shouldForceStopNative = false;

    try {
      // --- Perform Flutter state update immediately ---
      final alarmToRemove = _activeAlarms[alarmKey];

      if (alarmToRemove != null) {
        // 알람을 완전히 제거
        _activeAlarms.remove(alarmKey);
        logMessage(
          '[${alarmToRemove.busNo}] Flutter activeAlarms 목록에서 완전 제거',
          level: LogLevel.debug,
        );
      } else {
        logMessage(
          '⚠️ 취소 요청한 알람($alarmKey)이 Flutter 활성 알람 목록에 없음 (취소 전).',
          level: LogLevel.warning,
        );
      }

      // 자동 알람 목록에서도 제거
      final autoAlarmIndex = _autoAlarms.indexWhere(
        (alarm) =>
            alarm.busNo == busNo &&
            alarm.stationName == stationName &&
            alarm.routeId == routeId,
      );
      if (autoAlarmIndex != -1) {
        _autoAlarms.removeAt(autoAlarmIndex);
        logMessage(
          '[$busNo] Flutter autoAlarms 목록에서 완전 제거',
          level: LogLevel.debug,
        );
      }

      _cachedBusInfo.remove(cacheKey);
      logMessage('[$cacheKey] 버스 정보 캐시 즉시 제거', level: LogLevel.debug);

      // Check if the route being cancelled is the one being tracked OR if it's the last alarm
      if (_trackedRouteId == routeId) {
        _trackedRouteId = null;
        logMessage('추적 Route ID 즉시 초기화됨 (취소된 알람과 일치)', level: LogLevel.debug);
        if (_activeAlarms.isEmpty && _autoAlarms.isEmpty) {
          // 모든 알람이 없는 경우
          _isInTrackingMode = false;
          shouldForceStopNative = true; // Last tracked alarm removed
          logMessage('추적 모드 즉시 비활성화 (모든 알람 없음)', level: LogLevel.debug);
        } else {
          _isInTrackingMode = true;
          logMessage('다른 활성 알람 존재, 추적 모드 유지', level: LogLevel.debug);
          // Decide if we need to start tracking the next alarm? For now, no.
        }
      } else if (_activeAlarms.isEmpty && _autoAlarms.isEmpty) {
        // 모든 알람이 없는 경우
        // If the cancelled alarm wasn't the tracked one, but it was the *last* one
        _isInTrackingMode = false;
        _trackedRouteId = null;
        shouldForceStopNative = true; // Last alarm overall removed
        logMessage('마지막 알람 취소됨, 추적 모드 비활성화', level: LogLevel.debug);
      }

      await _saveAlarms(); // Persist the removal immediately
      await _saveAutoAlarms(); // 자동 알람 상태도 저장
      notifyListeners(); // Update UI immediately
      logMessage(
        '[$alarmKey] Flutter 상태 즉시 업데이트 및 리스너 알림 완료',
        level: LogLevel.debug,
      );
      // --- End immediate Flutter state update ---

      // --- Send request to Native ---
      try {
        if (shouldForceStopNative) {
          logMessage('마지막 알람 취소됨, 네이티브 강제 전체 중지 요청', level: LogLevel.debug);
          await _methodChannel?.invokeMethod('forceStopTracking');
          logMessage('✅ 네이티브 강제 전체 중지 요청 전송 완료', level: LogLevel.debug);
        } else {
          // If not the last alarm, just cancel the specific notification/route tracking
          logMessage(
            '다른 알람 존재, 네이티브 특정 알람($routeId) 취소 요청',
            level: LogLevel.debug,
          );
          await _methodChannel?.invokeMethod('cancelAlarmNotification', {
            'routeId': routeId,
            'busNo': busNo,
            'stationName': stationName,
          });
          logMessage('✅ 네이티브 특정 알람 취소 요청 전송 완료', level: LogLevel.debug);
        }
      } catch (nativeError) {
        logMessage('❌ 네이티브 요청 전송 오류: $nativeError', level: LogLevel.error);
        return false; // Indicate that the native part failed
      }
      // --- End Native request ---

      return true; // Return true as the action was initiated and Flutter state updated.
    } catch (e) {
      logMessage('❌ 알람 취소 처리 중 오류 (Flutter 업데이트): $e', level: LogLevel.error);
      notifyListeners();
      return false;
    }
  }

  Future<bool> refreshAutoAlarmBusInfo(AutoAlarm alarm) async {
    try {
      if (!alarm.isActive) {
        logMessage('비활성화된 알람은 정보를 업데이트하지 않습니다', level: LogLevel.debug);
        return false;
      }

      logMessage(
        '🔄 자동 알람 버스 정보 업데이트 시작: [36m${alarm.routeNo}번, ${alarm.stationName}[0m',
        level: LogLevel.debug,
      );

      // ✅ stationId 보정 로직 개선 (DB 실패 시 매핑 사용)
      String effectiveStationId = alarm.stationId;
      if (effectiveStationId.isEmpty ||
          effectiveStationId.length < 10 ||
          !effectiveStationId.startsWith('7')) {
        // 먼저 매핑을 통해 stationId 가져오기
        effectiveStationId = _getStationIdFromName(
          alarm.stationName,
          alarm.routeId,
        );

        // DB를 통한 추가 보정 시도 (선택사항)
        try {
          final dbHelper = DatabaseHelper();
          final resolvedStationId = await dbHelper.getStationIdFromWincId(
            alarm.stationName,
          );
          if (resolvedStationId != null && resolvedStationId.isNotEmpty) {
            effectiveStationId = resolvedStationId;
            logMessage(
              '✅ 자동 알람 DB stationId 보정: ${alarm.stationName} → $effectiveStationId',
              level: LogLevel.debug,
            );
          } else {
            logMessage(
              '⚠️ DB stationId 보정 실패, 매핑값 사용: ${alarm.stationName} → $effectiveStationId',
              level: LogLevel.debug,
            );
          }
        } catch (e) {
          logMessage(
            '❌ DB stationId 보정 중 오류, 매핑값 사용: $e → $effectiveStationId',
            level: LogLevel.warning,
          );
        }

        // 매핑도 실패한 경우에만 오류 처리
        if (effectiveStationId.isEmpty || effectiveStationId == alarm.routeId) {
          logMessage(
            '❌ stationId 보정 완전 실패: ${alarm.stationName}',
            level: LogLevel.error,
          );
          return false;
        }
      }

      // ✅ API 호출을 통한 버스 실시간 정보 가져오기
      try {
        const methodChannel = MethodChannel(
          'com.example.daegu_bus_app/bus_api',
        );
        final result = await methodChannel.invokeMethod(
          'getBusArrivalByRouteId',
          {'stationId': effectiveStationId, 'routeId': alarm.routeId},
        );

        logMessage(
          '🚌 [API 응답] 자동 알람 응답 수신: ${result?.runtimeType}',
          level: LogLevel.debug,
        );

        if (result != null) {
          try {
            // ✅ 응답 파싱 로직 개선
            dynamic parsedData;
            List<dynamic> arrivals = [];

            // 응답 타입별 처리
            if (result is String) {
              logMessage('🚌 [API 파싱] String 형식 응답 처리', level: LogLevel.debug);
              try {
                parsedData = jsonDecode(result);
              } catch (e) {
                logMessage('❌ JSON 파싱 오류: $e', level: LogLevel.error);
                return false;
              }
            } else if (result is List) {
              logMessage('🚌 [API 파싱] List 형식 응답 처리', level: LogLevel.debug);
              parsedData = result;
            } else if (result is Map) {
              logMessage('🚌 [API 파싱] Map 형식 응답 처리', level: LogLevel.debug);
              parsedData = result;
            } else {
              logMessage(
                '❌ 지원되지 않는 응답 타입: ${result.runtimeType}',
                level: LogLevel.error,
              );
              return false;
            }

            // ✅ parsedData 구조 분석 및 arrivals 추출
            if (parsedData is List) {
              arrivals = parsedData;
            } else if (parsedData is Map) {
              // 자동 알람 응답 형식: { "routeNo": "623", "arrList": [...] }
              if (parsedData.containsKey('arrList')) {
                arrivals = parsedData['arrList'] as List? ?? [];
                logMessage(
                  '🚌 [API 파싱] arrList에서 도착 정보 추출: ${arrivals.length}개',
                  level: LogLevel.debug,
                );
              } else if (parsedData.containsKey('bus')) {
                arrivals = parsedData['bus'] as List? ?? [];
                logMessage(
                  '🚌 [API 파싱] bus에서 도착 정보 추출: ${arrivals.length}개',
                  level: LogLevel.debug,
                );
              } else {
                logMessage(
                  '❌ 예상치 못한 Map 구조: ${parsedData.keys}',
                  level: LogLevel.error,
                );
                return false;
              }
            }

            logMessage(
              '🚌 [API 파싱] 파싱된 arrivals: ${arrivals.length}개 항목',
              level: LogLevel.debug,
            );

            if (arrivals.isNotEmpty) {
              // ✅ 버스 정보 추출 및 필터링
              dynamic busInfo;
              bool found = false;

              // 알람에 설정된 노선 번호와 일치하는 버스 찾기
              for (var bus in arrivals) {
                if (bus is Map) {
                  final busRouteNo = bus['routeNo']?.toString() ?? '';
                  final busRouteId = bus['routeId']?.toString() ?? '';
                  // routeNo 또는 routeId로 매칭
                  if (busRouteNo == alarm.routeNo ||
                      busRouteId == alarm.routeId) {
                    busInfo = bus;
                    found = true;
                    logMessage(
                      '✅ 일치하는 노선 찾음: ${alarm.routeNo} (routeNo: $busRouteNo, routeId: $busRouteId)',
                      level: LogLevel.debug,
                    );
                    break;
                  }
                }
              }

              // 일치하는 노선이 없으면 첫 번째 항목 사용
              if (!found && arrivals.isNotEmpty) {
                busInfo = arrivals.first;
                final routeNo = busInfo['routeNo']?.toString() ?? '정보 없음';
                logMessage(
                  '⚠️ 일치하는 노선 없음, 첫 번째 항목 사용: $routeNo',
                  level: LogLevel.warning,
                );
              }

              if (busInfo != null) {
                // ✅ 도착 정보 추출 - 다양한 필드명 지원
                final estimatedTime = busInfo['arrState'] ??
                    busInfo['estimatedTime'] ??
                    busInfo['도착예정소요시간'] ??
                    "정보 없음";

                final currentStation = busInfo['bsNm'] ??
                    busInfo['currentStation'] ??
                    busInfo['현재정류소'] ??
                    '정보 없음';

                final int remainingMinutes = _parseRemainingMinutes(
                  estimatedTime,
                );

                logMessage(
                  '🚌 [정보 추출] estimatedTime: $estimatedTime, currentStation: $currentStation, remainingMinutes: $remainingMinutes',
                  level: LogLevel.debug,
                );

                // ✅ 캐시에 저장
                final cachedInfo = CachedBusInfo(
                  remainingMinutes: remainingMinutes,
                  currentStation: currentStation,
                  stationName: alarm.stationName,
                  busNo: alarm.routeNo,
                  routeId: alarm.routeId,
                  lastUpdated: DateTime.now(),
                );

                final key = "${alarm.routeNo}_${alarm.routeId}";
                _cachedBusInfo[key] = cachedInfo;

                logMessage(
                  '✅ 자동 알람 버스 정보 업데이트 완료: ${alarm.routeNo}번, $remainingMinutes분 후 도착, 위치: $currentStation',
                  level: LogLevel.info,
                );

                // ✅ 알림 업데이트

                // 자동 알람에서 Flutter 알림 제거 - BusAlertService가 모든 알림 처리
                logMessage(
                  '✅ 자동 알람 정보 업데이트: ${alarm.routeNo}번, $remainingMinutes분 후, $currentStation',
                  level: LogLevel.debug,
                );

                // ✅ 버스 모니터링 서비스 시작 (10분 이내일 때)
                if (remainingMinutes <= 10 && remainingMinutes >= 0) {
                  try {
                    await startBusMonitoringService(
                      routeId: alarm.routeId,
                      stationId: effectiveStationId,
                      busNo: alarm.routeNo,
                      stationName: alarm.stationName,
                    );
                    logMessage(
                      '✅ 자동 알람 버스 모니터링 시작: ${alarm.routeNo}번 ($remainingMinutes분 후 도착)',
                      level: LogLevel.info,
                    );
                  } catch (e) {
                    logMessage(
                      '❌ 자동 알람 버스 모니터링 시작 실패: $e',
                      level: LogLevel.error,
                    );
                  }
                }

                // UI 업데이트
                notifyListeners();
                return true;
              }
            } else {
              logMessage('⚠️ 도착 정보 없음', level: LogLevel.warning);
            }
          } catch (e) {
            logMessage('❌ 버스 정보 파싱 오류: $e', level: LogLevel.error);
            logMessage('원본 응답: $result', level: LogLevel.debug);
          }
        } else {
          logMessage('⚠️ API 응답이 null입니다', level: LogLevel.warning);
        }
      } catch (e) {
        logMessage('❌ 버스 API 호출 오류: $e', level: LogLevel.error);
      }

      return false;
    } catch (e) {
      logMessage('❌ 자동 알람 버스 정보 업데이트 오류: $e', level: LogLevel.error);
      return false;
    }
  }

  // ✅ 문자열 형태의 도착 시간을 분 단위 정수로 변환하는 메서드 개선
  int _parseRemainingMinutes(dynamic estimatedTime) {
    if (estimatedTime == null) return -1;

    final String timeStr = estimatedTime.toString().trim();

    // 곧 도착 관련
    if (timeStr == '곧 도착' || timeStr == '전' || timeStr == '도착') return 0;

    // 운행 종료 관련
    if (timeStr == '운행종료' || timeStr == '-' || timeStr == '운행종료.') return -1;

    // 출발 예정 관련
    if (timeStr.contains('출발예정') || timeStr.contains('기점출발')) return -1;

    // 숫자 + '분' 형태 처리
    if (timeStr.contains('분')) {
      final numericValue = timeStr.replaceAll(RegExp(r'[^0-9]'), '');
      return numericValue.isEmpty ? -1 : int.tryParse(numericValue) ?? -1;
    }

    // 순수 숫자인 경우
    final numericValue = timeStr.replaceAll(RegExp(r'[^0-9]'), '');
    if (numericValue.isNotEmpty) {
      final minutes = int.tryParse(numericValue);
      if (minutes != null && minutes >= 0 && minutes <= 180) {
        // 3시간 이내만 유효
        return minutes;
      }
    }

    logMessage('⚠️ 파싱할 수 없는 도착 시간 형식: "$timeStr"', level: LogLevel.warning);
    return -1;
  }

  /// 정류장 이름으로 stationId 매핑
  String _getStationIdFromName(String stationName, String fallbackRouteId) {
    // 알려진 정류장 이름과 stationId 매핑
    final Map<String, String> stationMapping = {
      '새동네아파트앞': '7021024000',
      '새동네아파트건너': '7021023900',
      '칠성고가도로하단': '7021051300',
      '대구삼성창조캠퍼스3': '7021011000',
      '대구삼성창조캠퍼스': '7021011200',
      '동대구역': '7021052100',
      '동대구역건너': '7021052000',
      '경명여고건너': '7021024200',
      '경명여고': '7021024100',
    };

    // 정확한 매칭 시도
    if (stationMapping.containsKey(stationName)) {
      return stationMapping[stationName]!;
    }

    // 부분 매칭 시도
    for (var entry in stationMapping.entries) {
      if (stationName.contains(entry.key) || entry.key.contains(stationName)) {
        return entry.value;
      }
    }

    // 매칭 실패 시 fallback 사용
    return fallbackRouteId;
  }

  Future<void> updateAutoAlarms(List<AutoAlarm> autoAlarms) async {
    try {
      // 백그라운드 메신저 상태 확인 및 초기화
      if (!kIsWeb) {
        try {
          final rootIsolateToken = RootIsolateToken.instance;
          if (rootIsolateToken != null) {
            BackgroundIsolateBinaryMessenger.ensureInitialized(
              rootIsolateToken,
            );
          }
        } catch (e) {
          logMessage(
            '⚠️ updateAutoAlarms - BackgroundIsolateBinaryMessenger 초기화 오류 (무시): $e',
            level: LogLevel.warning,
          );
        }
      }

      logMessage('🔄 자동 알람 업데이트 시작: ${autoAlarms.length}개');
      _autoAlarms.clear();

      for (var alarm in autoAlarms) {
        logMessage('📝 알람 처리 중: ${alarm.routeNo}번, ${alarm.stationName}');

        if (!alarm.isActive) {
          logMessage('  ⚠️ 비활성화된 알람 건너뛰기');
          continue;
        }

        final DateTime? scheduledTime = alarm.getNextAlarmTime();

        if (scheduledTime == null) {
          logMessage(
            '  ⚠️ 유효한 다음 알람 시간을 찾지 못함: ${alarm.routeNo}',
            level: LogLevel.warning,
          );
          continue;
        }

        final now = DateTime.now();
        final timeUntilAlarm = scheduledTime.difference(now);
        logMessage('  ⏰ 다음 알람까지 ${timeUntilAlarm.inMinutes}분 남음');

        if (timeUntilAlarm.inSeconds <= 30 &&
            timeUntilAlarm.inSeconds >= -300) {
          logMessage('  ⚡ 알람 시간이 지났음 - 즉시 실행 (${timeUntilAlarm.inSeconds}초)');
          await _executeAutoAlarmImmediately(alarm);
        }

        final alarmData = alarm_model.AlarmData(
          id: alarm.id,
          busNo: alarm.routeNo,
          stationName: alarm.stationName,
          remainingMinutes: 0,
          routeId: alarm.routeId,
          scheduledTime: scheduledTime,
          useTTS: alarm.useTTS,
          isAutoAlarm: true,
          repeatDays: alarm.repeatDays, // 반복 요일 정보 포함
        );
        _autoAlarms.add(alarmData);
        logMessage('  ✅ 알람 데이터 생성 완료');

        await _scheduleAutoAlarm(alarm, scheduledTime);
      }

      await _saveAutoAlarms();
      logMessage('✅ 자동 알람 업데이트 완료: ${_autoAlarms.length}개');
    } catch (e) {
      logMessage('❌ 자동 알람 업데이트 오류: $e', level: LogLevel.error);
      logMessage('  - 스택 트레이스: ${e is Error ? e.stackTrace : "없음"}');
    }
  }
}
